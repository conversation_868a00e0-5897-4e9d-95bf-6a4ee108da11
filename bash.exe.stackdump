Stack trace:
Frame         Function      Args
0007FFFF9E00  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8D00) msys-2.0.dll+0x1FE8E
0007FFFF9E00  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA0D8) msys-2.0.dll+0x67F9
0007FFFF9E00  000210046832 (000210286019, 0007FFFF9CB8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9E00  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9E00  000210068E24 (0007FFFF9E10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA0E0  00021006A225 (0007FFFF9E10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCC3760000 ntdll.dll
7FFCC29B0000 KERNEL32.DLL
7FFCC0B90000 KERNELBASE.dll
7FFCBC860000 apphelp.dll
7FFCC2D10000 USER32.dll
7FFCC0F60000 win32u.dll
7FFCC2980000 GDI32.dll
7FFCC1110000 gdi32full.dll
000210040000 msys-2.0.dll
7FFCC0AE0000 msvcp_win.dll
7FFCC13D0000 ucrtbase.dll
7FFCC19F0000 advapi32.dll
7FFCC28D0000 msvcrt.dll
7FFCC2C50000 sechost.dll
7FFCC26B0000 RPCRT4.dll
7FFCBFDF0000 CRYPTBASE.DLL
7FFCC0980000 bcryptPrimitives.dll
7FFCC2A80000 IMM32.DLL
